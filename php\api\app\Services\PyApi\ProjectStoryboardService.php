<?php

namespace App\Services\PyApi;

use App\Models\Project;
use App\Models\ProjectStoryboard;
use App\Models\ProjectScenario;
use App\Models\ProjectCharacter;
use App\Models\StoryboardCharacter;
use App\Models\StyleLibrary;
use App\Enums\ApiCodeEnum;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Helpers\LogCheckHelper;
use Carbon\Carbon;

/**
 * 项目分镜业务服务层
 * 处理分镜相关的业务逻辑
 */
class ProjectStoryboardService
{
    /**
     * 检查项目访问权限
     */
    public function checkProjectAccess(int $userId, int $projectId): array
    {
        try {
            $project = Project::where('id', $projectId)
                ->where('user_id', $userId)
                ->first();

            if (!$project) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '项目不存在或无权访问',
                    'data' => []
                ];
            }

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '权限验证通过',
                'data' => ['project' => $project]
            ];

        } catch (\Exception $e) {
            Log::error('项目权限检查失败', [
                'method' => __METHOD__,
                'user_id' => $userId,
                'project_id' => $projectId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '权限检查失败',
                'data' => []
            ];
        }
    }

    /**
     * 获取项目分镜列表
     */
    public function getProjectStoryboards(int $projectId, array $filters = [], int $page = 1, int $perPage = 10): array
    {
        try {
            $query = ProjectStoryboard::byProject($projectId)
                ->with(['characters'])
                ->orderByScene();

            // 应用筛选条件
            if (!empty($filters['status'])) {
                $query->byStatus($filters['status']);
            }

            // 分页查询
            $storyboards = $query->paginate($perPage, ['*'], 'page', $page);

            $data = [
                'storyboards' => $storyboards->items(),
                'pagination' => [
                    'current_page' => $storyboards->currentPage(),
                    'per_page' => $storyboards->perPage(),
                    'total' => $storyboards->total(),
                    'last_page' => $storyboards->lastPage(),
                    'has_more' => $storyboards->hasMorePages()
                ]
            ];

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '获取分镜列表成功',
                'data' => $data
            ];

        } catch (\Exception $e) {
            Log::error('获取项目分镜列表失败', [
                'method' => __METHOD__,
                'project_id' => $projectId,
                'filters' => $filters,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取分镜列表失败',
                'data' => []
            ];
        }
    }

    /**
     * 获取分镜详情
     */
    public function getStoryboardDetail(int $storyboardId, int $userId): array
    {
        try {
            $storyboard = ProjectStoryboard::with(['project', 'characters.character', 'characters.projectCharacter'])
                ->find($storyboardId);

            if (!$storyboard) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '分镜不存在',
                    'data' => []
                ];
            }

            // 检查权限
            if ($storyboard->project->user_id !== $userId) {
                return [
                    'code' => ApiCodeEnum::FORBIDDEN,
                    'message' => '无权访问该分镜',
                    'data' => []
                ];
            }

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '获取分镜详情成功',
                'data' => ['storyboard' => $storyboard]
            ];

        } catch (\Exception $e) {
            Log::error('获取分镜详情失败', [
                'method' => __METHOD__,
                'storyboard_id' => $storyboardId,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取分镜详情失败',
                'data' => []
            ];
        }
    }

    /**
     * 创建分镜
     */
    public function createStoryboard(int $userId, int $projectId, array $storyboardData): array
    {
        try {
            DB::beginTransaction();

            // 验证项目权限
            $projectCheck = $this->checkProjectAccess($userId, $projectId);
            if ($projectCheck['code'] !== ApiCodeEnum::SUCCESS) {
                return $projectCheck;
            }

            // 如果没有指定分镜序号，自动生成
            if (empty($storyboardData['scene_number'])) {
                $maxSceneNumber = ProjectStoryboard::byProject($projectId)->max('scene_number') ?? 0;
                $storyboardData['scene_number'] = $maxSceneNumber + 1;
            } else {
                // 检查序号是否已存在
                $existingStoryboard = ProjectStoryboard::byProject($projectId)
                    ->where('scene_number', $storyboardData['scene_number'])
                    ->first();

                if ($existingStoryboard) {
                    return [
                        'code' => ApiCodeEnum::VALIDATION_ERROR,
                        'message' => '分镜序号已存在',
                        'data' => []
                    ];
                }
            }

            // 创建分镜
            $storyboard = ProjectStoryboard::create([
                'project_id' => $projectId,
                'scenarios_id' => $storyboardData['scenarios_id'],
                'scene_number' => $storyboardData['scene_number'],
                'scene_title' => $storyboardData['scene_title'],
                'subtitle' => $storyboardData['subtitle'],
                'status' => ProjectStoryboard::STATUS_DRAFT
            ]);

            DB::commit();

            Log::info('分镜创建成功', [
                'storyboard_id' => $storyboard->id,
                'project_id' => $projectId,
                'user_id' => $userId
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '分镜创建成功',
                'data' => ['storyboard' => $storyboard]
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('创建分镜失败', [
                'method' => __METHOD__,
                'user_id' => $userId,
                'project_id' => $projectId,
                'storyboard_data' => LogCheckHelper::sanitize_request_for_log($storyboardData),
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '创建分镜失败',
                'data' => []
            ];
        }
    }

    /**
     * 更新分镜
     */
    public function updateStoryboard(int $storyboardId, int $userId, array $updateData): array
    {
        try {
            DB::beginTransaction();

            $storyboard = ProjectStoryboard::with('project')->find($storyboardId);

            if (!$storyboard) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '分镜不存在',
                    'data' => []
                ];
            }

            // 检查权限
            if ($storyboard->project->user_id !== $userId) {
                return [
                    'code' => ApiCodeEnum::FORBIDDEN,
                    'message' => '无权修改该分镜',
                    'data' => []
                ];
            }

            // 如果更新分镜序号，检查是否冲突
            if (isset($updateData['scene_number']) && $updateData['scene_number'] !== $storyboard->scene_number) {
                $existingStoryboard = ProjectStoryboard::byProject($storyboard->project_id)
                    ->where('scene_number', $updateData['scene_number'])
                    ->where('id', '!=', $storyboardId)
                    ->first();

                if ($existingStoryboard) {
                    return [
                        'code' => ApiCodeEnum::VALIDATION_ERROR,
                        'message' => '分镜序号已存在',
                        'data' => []
                    ];
                }
            }

            // 更新分镜
            $storyboard->update($updateData);

            DB::commit();

            Log::info('分镜更新成功', [
                'storyboard_id' => $storyboardId,
                'user_id' => $userId,
                'update_data' => LogCheckHelper::sanitize_request_for_log($updateData)
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '分镜更新成功',
                'data' => ['storyboard' => $storyboard->fresh()]
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('更新分镜失败', [
                'method' => __METHOD__,
                'storyboard_id' => $storyboardId,
                'user_id' => $userId,
                'update_data' => LogCheckHelper::sanitize_request_for_log($updateData),
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '更新分镜失败',
                'data' => []
            ];
        }
    }

    /**
     * 删除分镜
     */
    public function deleteStoryboard(int $storyboardId, int $userId): array
    {
        try {
            DB::beginTransaction();

            $storyboard = ProjectStoryboard::with('project')->find($storyboardId);

            if (!$storyboard) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '分镜不存在',
                    'data' => []
                ];
            }

            // 检查权限
            if ($storyboard->project->user_id !== $userId) {
                return [
                    'code' => ApiCodeEnum::FORBIDDEN,
                    'message' => '无权删除该分镜',
                    'data' => []
                ];
            }

            // 删除分镜（会级联删除相关的角色关联）
            $storyboard->delete();

            DB::commit();

            Log::info('分镜删除成功', [
                'storyboard_id' => $storyboardId,
                'user_id' => $userId
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '分镜删除成功',
                'data' => []
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('删除分镜失败', [
                'method' => __METHOD__,
                'storyboard_id' => $storyboardId,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '删除分镜失败',
                'data' => []
            ];
        }
    }

    /**
     * 批量更新分镜排序
     */
    public function reorderStoryboards(int $userId, int $projectId, array $storyboards): array
    {
        try {
            DB::beginTransaction();

            // 验证项目权限
            $projectCheck = $this->checkProjectAccess($userId, $projectId);
            if ($projectCheck['code'] !== ApiCodeEnum::SUCCESS) {
                return $projectCheck;
            }

            // 验证所有分镜都属于该项目
            $storyboardIds = array_column($storyboards, 'id');
            $existingStoryboards = ProjectStoryboard::byProject($projectId)
                ->whereIn('id', $storyboardIds)
                ->get();

            if ($existingStoryboards->count() !== count($storyboardIds)) {
                return [
                    'code' => ApiCodeEnum::VALIDATION_ERROR,
                    'message' => '部分分镜不存在或不属于该项目',
                    'data' => []
                ];
            }

            // 批量更新排序
            foreach ($storyboards as $storyboardData) {
                ProjectStoryboard::where('id', $storyboardData['id'])
                    ->update(['scene_number' => $storyboardData['scene_number']]);
            }

            DB::commit();

            Log::info('分镜排序更新成功', [
                'project_id' => $projectId,
                'user_id' => $userId,
                'storyboard_count' => count($storyboards)
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '分镜排序更新成功',
                'data' => []
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('分镜排序失败', [
                'method' => __METHOD__,
                'user_id' => $userId,
                'project_id' => $projectId,
                'storyboards' => LogCheckHelper::sanitize_request_for_log($storyboards),
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '分镜排序失败',
                'data' => []
            ];
        }
    }

    /**
     * 批量生成分镜
     */
    public function batchGenerateStoryboards(int $userId, int $projectId, array $storyboardIds = [], array $generationParams = []): array
    {
        try {
            DB::beginTransaction();

            // 验证项目权限
            $projectCheck = $this->checkProjectAccess($userId, $projectId);
            if ($projectCheck['code'] !== ApiCodeEnum::SUCCESS) {
                return $projectCheck;
            }

            // 获取要生成的分镜
            $query = ProjectStoryboard::byProject($projectId);

            if (!empty($storyboardIds)) {
                $query->whereIn('id', $storyboardIds);
            } else {
                // 如果没有指定分镜ID，则生成所有未生成的分镜
                $query->whereIn('status', [ProjectStoryboard::STATUS_DRAFT, ProjectStoryboard::STATUS_APPROVED]);
            }

            $storyboards = $query->get();

            if ($storyboards->isEmpty()) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '没有找到可生成的分镜',
                    'data' => []
                ];
            }

            $generatedCount = 0;
            $failedCount = 0;

            foreach ($storyboards as $storyboard) {
                try {
                    // 标记为生成中
                    $storyboard->markAsGenerating();

                    // 调用AI服务生成分镜图片
                    $this->generateStoryboardImage($storyboard, $generationParams, $userId);

                    $generatedCount++;
                } catch (\Exception $e) {
                    Log::error('单个分镜生成失败', [
                        'storyboard_id' => $storyboard->id,
                        'error' => $e->getMessage()
                    ]);

                    $storyboard->markAsFailed();
                    $failedCount++;
                }
            }

            DB::commit();

            Log::info('批量生成分镜完成', [
                'project_id' => $projectId,
                'user_id' => $userId,
                'generated_count' => $generatedCount,
                'failed_count' => $failedCount
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => "批量生成完成，成功：{$generatedCount}个，失败：{$failedCount}个",
                'data' => [
                    'generated_count' => $generatedCount,
                    'failed_count' => $failedCount,
                    'total_count' => $storyboards->count()
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('批量生成分镜失败', [
                'method' => __METHOD__,
                'user_id' => $userId,
                'project_id' => $projectId,
                'storyboard_ids' => $storyboardIds,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '批量生成分镜失败',
                'data' => []
            ];
        }
    }

    /**
     * 在AI分镜前创建项目
     *
     * @param int $userId 用户ID
     * @param int $styleId 风格库ID
     * @param string $aspectRatio 宽高比例
     * @return array
     */
    public function storyboardsUptodateProject(int $userId, int $styleId, string $aspectRatio): array
    {
        try {
            DB::beginTransaction();

            // 提取尺寸及风格信息（从用户提交的prompt数据中提取）
            $dimensionInfo = $this->extractDimensionInfoFromAiResponse($styleId, $aspectRatio);

            // 验证风格是否存在
            $style = StyleLibrary::active()->find($styleId);
            if (!$style) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '风格不存在',
                    'data' => []
                ];
            }

            // 补充风格信息到dimensionInfo
            $dimensionInfo['style_name'] = $style->name;
            $dimensionInfo['style_config'] = $style->style_config ?? [];
            $dimensionInfo['prompt_template'] = $style->prompt_template ?? '';

            // 1. 查询user_id=$userId and status=draft的项目，存在则返回项目ID
            $existingProject = Project::where('user_id', $userId)
                ->where('status', Project::STATUS_DRAFT)
                ->first();

            if ($existingProject) {
                // 更新现有项目的配置信息
                $existingProject->style_id = $styleId;
                $existingProject->project_config = array_merge(
                    $existingProject->project_config ?? [],
                    $dimensionInfo
                );
                $existingProject->last_accessed_at = Carbon::now();
                $existingProject->save();

                DB::commit();

                Log::info('返回现有草稿项目', [
                    'project_id' => $existingProject->id,
                    'user_id' => $userId,
                    'style_id' => $styleId,
                    'aspect_ratio' => $aspectRatio
                ]);

                return [
                    'code' => ApiCodeEnum::SUCCESS,
                    'message' => '返回现有草稿项目',
                    'data' => [
                        'project_id' => $existingProject->id,
                        'title' => $existingProject->title,
                        'status' => $existingProject->status,
                        'style_id' => $existingProject->style_id,
                        'project_config' => $existingProject->project_config,
                        'is_existing' => true
                    ]
                ];
            }

            // 2. 没有status=draft则创建新项目
            $title = $userId . '-待创建分镜';

            $project = Project::create([
                'user_id' => $userId,
                'title' => $title,
                'description' => '等待AI生成分镜内容的项目',
                'style_id' => $styleId,
                'status' => Project::STATUS_DRAFT,
                'project_config' => $dimensionInfo,
                'last_accessed_at' => Carbon::now()
            ]);

            // 增加风格使用次数
            $style->incrementUsage();

            DB::commit();

            Log::info('创建新的分镜项目', [
                'project_id' => $project->id,
                'user_id' => $userId,
                'style_id' => $styleId,
                'title' => $title,
                'aspect_ratio' => $aspectRatio
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '分镜项目创建成功',
                'data' => [
                    'project_id' => $project->id,
                    'title' => $project->title,
                    'status' => $project->status,
                    'style_id' => $project->style_id,
                    'project_config' => $project->project_config,
                    'is_existing' => false
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            $error_context = [
                'user_id' => $userId,
                'style_id' => $styleId,
                'aspect_ratio' => $aspectRatio,
            ];

            Log::error('分镜项目创建失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '分镜项目创建失败',
                'data' => null
            ];
        }
    }

    /**
     * 从故事内容拆解分镜（增强版：支持场景创建和项目更新）
     *
     * @param int $userId 用户ID
     * @param int $projectId 项目ID
     * @param string $storyContent AI返回的结构化JSON格式分镜剧本
     * @return array
     */
    public function extractStoryboardsFromStory(int $userId, int $projectId, string $storyContent): array
    {
        try {
            DB::beginTransaction();

            // 直接解析AI返回的结构化JSON格式分镜剧本（不再调用AI）
            $analysisResult = $this->parseAiStoryboardJson($storyContent);

            // 更新项目信息
            $projectService = new \App\Services\PyApi\ProjectService();
            $projectResult = $projectService->updateProject($projectId, $userId, $analysisResult['project_info']);

            // 创建场景
            $createdScenarios = $this->batchCreateScenarios($projectId, $analysisResult['scenarios']);

            // 创建分镜（关联场景）
            $createdStoryboards = $this->batchCreateStoryboards($projectId, $analysisResult['storyboards'], $createdScenarios);

            DB::commit();

            Log::info('故事分镜拆解成功', [
                'project_id' => $projectId,
                'scenario_count' => count($createdScenarios),
                'storyboard_count' => count($createdStoryboards)
            ]);

            return [
                'code' => \App\Enums\ApiCodeEnum::SUCCESS,
                'message' => '故事分镜拆解成功',
                'data' => [
                    'project_id' => $projectId,
                    'project' => $projectResult['data'],
                    'scenarios' => $createdScenarios,
                    'storyboards' => $createdStoryboards,
                    'scenario_count' => count($createdScenarios),
                    'storyboard_count' => count($createdStoryboards)
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('故事分镜拆解失败', [
                'method' => __METHOD__,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => \App\Enums\ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '故事分镜拆解失败',
                'data' => []
            ];
        }
    }

    /**
     * 解析AI返回的结构化JSON格式分镜剧本
     *
     * 传入的 $storyContent 应该是AI返回的结构化JSON格式，包含：
     * - 故事标题
     * - 故事简概
     * - 场景1, 场景2, 场景3...
     * 每个场景包含：场景名称、空间、时间、天气、场景提示词、分镜数组
     * 每个分镜包含：分镜序号、出境角色、字幕、分镜提示词
     *
     * @param string $storyContent AI返回的JSON格式分镜剧本
     * @return array 包含项目信息、场景信息、分镜信息和尺寸信息
     */
    private function parseAiStoryboardJson(string $storyContent): array
    {
        try {
            // 解析JSON格式的分镜剧本
            $parsedData = json_decode($storyContent, true);

            if (!is_array($parsedData)) {
                throw new \Exception('分镜剧本JSON格式无效');
            }

            // 验证数据完整性
            $this->validateAiStoryboardData($parsedData);

            // 提取项目创建信息(提取项目标题及简介信息)
            $projectInfo = $this->extractProjectInfoFromAiResponse($parsedData);

            // 提取场景信息
            $scenarios = $this->extractScenesFromAiResponse($parsedData);

            // 提取分镜信息
            $storyboards = $this->extractStoryboardsFromAiResponse($parsedData, $scenarios);

            return [
                'project_info' => $projectInfo,
                'scenarios' => $scenarios,
                'storyboards' => $storyboards
            ];

        } catch (\Exception $e) {
            Log::error('AI分镜剧本解析失败', [
                'error' => $e->getMessage(),
                'story_content' => substr($storyContent, 0, 200) . '...'
            ]);
            throw $e;
        }
    }

    /**
     * 验证分镜数据完整性
     */
    private function validateStoryboardData(array $data): void
    {
        // 检查是否包含场景信息
        if (!isset($data['scenarios']) || !is_array($data['scenarios'])) {
            throw new \Exception('缺少场景信息');
        }

        // 检查是否包含分镜信息
        if (!isset($data['storyboards']) || !is_array($data['storyboards'])) {
            throw new \Exception('缺少分镜信息');
        }

        // 验证场景数量
        if (count($data['scenarios']) === 0) {
            throw new \Exception('至少需要一个场景');
        }

        // 验证分镜数量
        if (count($data['storyboards']) === 0) {
            throw new \Exception('至少需要一个分镜');
        }

        // 验证每个场景的必需字段
        foreach ($data['scenarios'] as $index => $scenario) {
            $requiredFields = ['scene_name', 'space', 'time', 'weather'];
            foreach ($requiredFields as $field) {
                if (!isset($scenario[$field]) || empty($scenario[$field])) {
                    throw new \Exception("场景 {$index} 缺少必需字段: {$field}");
                }
            }
        }

        // 验证每个分镜的必需字段
        foreach ($data['storyboards'] as $index => $storyboard) {
            $requiredFields = ['scene_title', 'subtitle', 'ai_prompt'];
            foreach ($requiredFields as $field) {
                if (!isset($storyboard[$field]) || empty($storyboard[$field])) {
                    throw new \Exception("分镜 {$index} 缺少必需字段: {$field}");
                }
            }
        }
    }

    /**
     * 从AI响应中提取场景信息
     */
    private function extractScenesFromResponse(array $data): array
    {
        $scenarios = [];
        $scenariosData = $data['scenarios'] ?? [];

        foreach ($scenariosData as $index => $scenarioData) {
            $scenarios[] = [
                'scene_name' => $scenarioData['scene_name'],
                'space' => $scenarioData['space'],
                'time' => $scenarioData['time'],
                'weather' => $scenarioData['weather'],
                'scene_prompt' => $scenarioData['scene_prompt'] ?? null,
                'scene_order' => $index + 1,
                'metadata' => $scenarioData['metadata'] ?? null
            ];
        }

        return $scenarios;
    }

    /**
     * 从AI响应中提取分镜信息
     */
    private function extractStoryboardsFromResponse(array $data, array $scenarios): array
    {
        $storyboards = [];
        $storyboardsData = $data['storyboards'] ?? [];

        foreach ($storyboardsData as $index => $storyboardData) {
            // 根据分镜所属场景确定scenarios_id（这里需要根据实际业务逻辑调整）
            $scenarioIndex = $storyboardData['scenario_index'] ?? 0;
            $scenariosId = $scenarioIndex < count($scenarios) ? $scenarioIndex + 1 : 1;

            $storyboards[] = [
                'scenarios_id' => $scenariosId,
                'scene_number' => $index + 1,
                'scene_title' => $storyboardData['scene_title'],
                'subtitle' => $storyboardData['subtitle'] ?? $storyboardData['scene_description'] ?? '',
                'ai_prompt' => $storyboardData['ai_prompt'],
                'generation_params' => $storyboardData['generation_params'] ?? null,
                'metadata' => $storyboardData['metadata'] ?? null
            ];
        }

        return $storyboards;
    }

    /**
     * 验证AI返回的分镜剧本数据完整性
     * 适用于新的JSON格式：{"故事标题": "...", "故事简概": "...", "场景1": {...}, "场景2": {...}}
     */
    private function validateAiStoryboardData(array $data): void
    {
        // 检查基本结构
        if (!isset($data['故事标题']) || empty($data['故事标题'])) {
            throw new \Exception('缺少故事标题');
        }

        if (!isset($data['故事简概']) || empty($data['故事简概'])) {
            throw new \Exception('缺少故事简概');
        }

        // 查找场景数据
        $sceneCount = 0;
        foreach ($data as $key => $value) {
            if (preg_match('/^场景\d+$/', $key) && is_array($value)) {
                $sceneCount++;

                // 验证场景必需字段
                $requiredSceneFields = ['场景名称', '空间', '时间', '天气', '场景提示词', '分镜'];
                foreach ($requiredSceneFields as $field) {
                    if (!isset($value[$field]) || ($field !== '分镜' && empty($value[$field]))) {
                        throw new \Exception("场景 {$key} 缺少必需字段: {$field}");
                    }
                }

                // 验证分镜数组
                if (!is_array($value['分镜']) || empty($value['分镜'])) {
                    throw new \Exception("场景 {$key} 的分镜数组为空");
                }

                // 验证每个分镜的必需字段
                foreach ($value['分镜'] as $index => $storyboard) {
                    $requiredStoryboardFields = ['分镜序号', '出境角色', '字幕', '分镜提示词'];
                    foreach ($requiredStoryboardFields as $field) {
                        if (!isset($storyboard[$field]) || empty($storyboard[$field])) {
                            throw new \Exception("场景 {$key} 分镜 {$index} 缺少必需字段: {$field}");
                        }
                    }
                }
            }
        }

        if ($sceneCount === 0) {
            throw new \Exception('至少需要一个场景');
        }
    }

    /**
     * 从AI返回的JSON中提取项目创建信息
     * 提取故事标题、故事简概等项目基本信息
     */
    private function extractProjectInfoFromAiResponse(array $data): array
    {
        return [
            'title' => $data['故事标题'] ?? '',
            'description' => $data['故事简概'] ?? '',
            'story_content' => json_encode($data, JSON_UNESCAPED_UNICODE),
            'ai_generated_title' => $data['故事标题'] ?? '',
            'title_confirmed' => true, // 从AI分镜剧本创建的项目，标题已确认
        ];
    }

    /**
     * 从用户提交的prompt数据中提取尺寸和风格信息
     * @param int $styleId 风格库ID
     * @param string $aspectRatio 尺寸比例
     */
    private function extractDimensionInfoFromAiResponse(int $styleId, string $aspectRatio): array
    {
        $dimensionInfo = [
            'video_width' => 1920,      // 默认宽度
            'video_height' => 1080,     // 默认高度
            'aspect_ratio' => '16:9',   // 默认宽高比
            'resolution' => '1080p',    // 默认分辨率
            'frame_rate' => 30,         // 默认帧率
            'style_id' => $styleId,     // 风格库ID
            'style_name' => '',         // 风格名称
            'style_config' => [],       // 风格配置
            'prompt_template' => '',    // 风格提示词模板
        ];

        // 1. 处理尺寸信息
        $dimensionInfo['aspect_ratio'] = $aspectRatio;

        // 根据比例计算具体的宽高
        switch ($aspectRatio) {
            case '16:9':
                $dimensionInfo['video_width'] = 1920;
                $dimensionInfo['video_height'] = 1080;
                $dimensionInfo['resolution'] = '1080p';
                break;
            case '4:3':
                $dimensionInfo['video_width'] = 1024;
                $dimensionInfo['video_height'] = 768;
                $dimensionInfo['resolution'] = '768p';
                break;
            case '9:16':
                $dimensionInfo['video_width'] = 1080;
                $dimensionInfo['video_height'] = 1920;
                $dimensionInfo['resolution'] = '1080p';
                break;
            case '3:4':
                $dimensionInfo['video_width'] = 768;
                $dimensionInfo['video_height'] = 1024;
                $dimensionInfo['resolution'] = '768p';
                break;
            default:
                // 保持默认值
                break;
        }

        return $dimensionInfo;
    }

    /**
     * 从AI返回的JSON中提取场景信息
     * 适用于新的JSON格式：{"场景1": {...}, "场景2": {...}}
     */
    private function extractScenesFromAiResponse(array $data): array
    {
        $scenarios = [];
        $sceneOrder = 1;

        foreach ($data as $key => $value) {
            if (preg_match('/^场景(\d+)$/', $key, $matches) && is_array($value)) {
                $scenarios[] = [
                    'scene_name' => $value['场景名称'],
                    'space' => $value['空间'],
                    'time' => $value['时间'],
                    'weather' => $value['天气'],
                    'scene_prompt' => $value['场景提示词'],
                    'scene_order' => $sceneOrder++,
                    'metadata' => [
                        'original_key' => $key,
                        'scene_number' => intval($matches[1])
                    ]
                ];
            }
        }

        return $scenarios;
    }

    /**
     * 从AI返回的JSON中提取分镜信息
     * 适用于新的JSON格式，每个场景包含分镜数组
     */
    private function extractStoryboardsFromAiResponse(array $data, array $scenarios): array
    {
        $storyboards = [];
        $globalStoryboardNumber = 1;

        foreach ($data as $key => $value) {
            if (preg_match('/^场景(\d+)$/', $key, $matches) && is_array($value)) {
                $sceneNumber = intval($matches[1]);

                // 找到对应的场景索引
                $scenarioIndex = $sceneNumber - 1;

                if (isset($value['分镜']) && is_array($value['分镜'])) {
                    foreach ($value['分镜'] as $storyboardData) {
                        $storyboards[] = [
                            'scene_number' => $globalStoryboardNumber++,
                            'scene_title' => $value['场景名称'] . ' - 分镜' . $storyboardData['分镜序号'],
                            'subtitle' => $storyboardData['字幕'],
                            'ai_prompt' => $storyboardData['分镜提示词'],
                            'generation_params' => [
                                'characters' => $storyboardData['出境角色'],
                                'scene_context' => $value['场景名称'],
                                'scene_prompt' => $value['场景提示词'],
                                'storyboard_number' => $storyboardData['分镜序号']
                            ],
                            'metadata' => [
                                'original_scene_key' => $key,
                                'original_scene_number' => $sceneNumber,
                                'original_storyboard_number' => $storyboardData['分镜序号'],
                                'scenario_index' => $scenarioIndex
                            ]
                        ];
                    }
                }
            }
        }

        return $storyboards;
    }

    /**
     * 批量创建场景
     */
    private function batchCreateScenarios(int $projectId, array $scenariosData): array
    {
        $createdScenarios = [];

        foreach ($scenariosData as $scenarioData) {
            $scenario = ProjectScenario::create([
                'project_id' => $projectId,
                'scene_name' => $scenarioData['scene_name'],
                'space' => $scenarioData['space'],
                'time' => $scenarioData['time'],
                'weather' => $scenarioData['weather'],
                'scene_prompt' => $scenarioData['scene_prompt'],
                'scene_order' => $scenarioData['scene_order'],
                'metadata' => $scenarioData['metadata']
            ]);

            $createdScenarios[] = $scenario;
        }

        return $createdScenarios;
    }

    /**
     * 批量创建分镜记录
     */
    private function batchCreateStoryboards(int $projectId, array $storyboardsData, array $scenarios): array
    {
        $createdStoryboards = [];

        foreach ($storyboardsData as $storyboardData) {
            // 根据scenarios_id找到对应的场景
            $scenarioId = $storyboardData['scenarios_id'];
            $scenario = null;
            foreach ($scenarios as $s) {
                if ($s->scene_order == $scenarioId) {
                    $scenario = $s;
                    break;
                }
            }

            if (!$scenario) {
                // 如果找不到对应场景，使用第一个场景
                $scenario = $scenarios[0] ?? null;
            }

            if ($scenario) {
                $storyboard = ProjectStoryboard::create([
                    'project_id' => $projectId,
                    'scenarios_id' => $scenario->id,
                    'scene_number' => $storyboardData['scene_number'],
                    'scene_title' => $storyboardData['scene_title'],
                    'subtitle' => $storyboardData['subtitle'],
                    'ai_prompt' => $storyboardData['ai_prompt'],
                    'generation_params' => $storyboardData['generation_params'],
                    'metadata' => $storyboardData['metadata'],
                    'status' => ProjectStoryboard::STATUS_DRAFT
                ]);

                $createdStoryboards[] = $storyboard;
            }
        }

        return $createdStoryboards;
    }

    /**
     * 创建分镜角色关联
     */
    private function createStoryboardCharacters(int $storyboardId, array $charactersData): void
    {
        foreach ($charactersData as $characterData) {
            StoryboardCharacter::create([
                'storyboard_id' => $storyboardId,
                'character_name' => $characterData['character_name'],
                'character_description' => $characterData['character_description'] ?? null,
                'position_description' => $characterData['position_description'] ?? null,
                'action_description' => $characterData['action_description'] ?? null,
                'binding_status' => StoryboardCharacter::STATUS_UNBOUND
            ]);
        }
    }

    /**
     * 生成分镜图片
     *
     * 🚨 重构说明：
     * ✅ 调用 ImageService 进行图片生成，符合职责分离原则
     * ✅ ImageController 负责AI调用、积分处理、状态管理
     * ✅ ProjectStoryboardService 负责分镜业务逻辑
     */
    private function generateStoryboardImage(ProjectStoryboard $storyboard, array $generationParams, int $userId): void
    {
        try {
            // 获取分镜的提示词
            $prompt = $storyboard->ai_prompt ?: $storyboard->scene_description;

            // 构建图片生成参数
            $imageGenerationParams = [
                'style' => $generationParams['style'] ?? null,
                'aspect_ratio' => $generationParams['aspect_ratio'] ?? '16:9',
                'quality' => $generationParams['quality'] ?? 'standard',
                'platform' => $generationParams['platform'] ?? 'liblib'
            ];

            // 调用 ImageService 进行图片生成（这是正确的架构）
            $imageService = app(\App\Services\PyApi\ImageService::class);
            $result = $imageService->generateImage(
                $userId,
                $prompt,
                null, // character_id
                $storyboard->project_id,
                $imageGenerationParams
            );

            if ($result['code'] === \App\Enums\ApiCodeEnum::SUCCESS) {
                // 图片生成成功，标记为完成
                $storyboard->markAsCompleted();

                // 保存生成的资源ID（如果有）
                if (isset($result['data']['resource_id']) && $result['data']['resource_id']) {
                    $storyboard->generated_image_id = $result['data']['resource_id'];
                }

                // 保存生成信息到元数据中
                $metadata = $storyboard->metadata ?? [];
                $metadata['image_generation_result'] = [
                    'image_url' => $result['data']['image_url'] ?? '',
                    'thumbnail_url' => $result['data']['thumbnail_url'] ?? '',
                    'cost' => $result['data']['cost'] ?? 0,
                    'platform' => $result['data']['platform'] ?? '',
                    'generated_at' => now()->format('c')
                ];
                $storyboard->metadata = $metadata;
                $storyboard->save();
            } else {
                // 图片生成失败
                $storyboard->markAsFailed();
                throw new \Exception('图片生成失败：' . ($result['message'] ?? '未知错误'));
            }

        } catch (\Exception $e) {
            $storyboard->markAsFailed();
            throw $e;
        }
    }


    /**
     * 选择性生成分镜图片
     *
     * 🚨 架构边界规范：
     * ✅ 工具API接口服务负责调用AI服务，不进行模拟
     * ✅ 使用 AiServiceClient::callWithUserChoice 实现环境切换
     * ❌ 不在工具API中进行模拟返回数据
     */
    public function selectiveGenerateStoryboards(int $userId, int $projectId, array $filters = [], array $generationParams = []): array
    {
        try {
            DB::beginTransaction();

            // 验证项目权限
            $projectCheck = $this->checkProjectAccess($userId, $projectId);
            if ($projectCheck['code'] !== ApiCodeEnum::SUCCESS) {
                return $projectCheck;
            }

            // 构建查询条件
            $query = ProjectStoryboard::byProject($projectId);

            // 应用筛选条件
            if (!empty($filters['status'])) {
                $query->whereIn('status', $filters['status']);
            } else {
                // 默认只处理草稿和已批准的分镜
                $query->whereIn('status', [ProjectStoryboard::STATUS_DRAFT, ProjectStoryboard::STATUS_APPROVED]);
            }

            if (isset($filters['has_ai_prompt']) && $filters['has_ai_prompt']) {
                $query->whereNotNull('ai_prompt')->where('ai_prompt', '!=', '');
            }

            if (!empty($filters['scene_types'])) {
                // 这里可以根据场景类型进行筛选，需要根据实际的场景类型字段调整
                // 假设有scene_type字段或者通过scene_description进行模糊匹配
                $query->where(function($q) use ($filters) {
                    foreach ($filters['scene_types'] as $type) {
                        $q->orWhere('scene_description', 'like', "%{$type}%");
                    }
                });
            }

            $storyboards = $query->get();

            if ($storyboards->isEmpty()) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '没有找到符合筛选条件的分镜',
                    'data' => []
                ];
            }

            $generatedCount = 0;
            $failedCount = 0;
            $skippedCount = 0;

            foreach ($storyboards as $storyboard) {
                try {
                    // 检查是否正在生成中
                    if ($storyboard->status === ProjectStoryboard::STATUS_GENERATING) {
                        $skippedCount++;
                        continue;
                    }

                    // 标记为生成中
                    $storyboard->markAsGenerating();

                    // 调用AI服务生成分镜图片
                    $this->generateStoryboardImage($storyboard, $generationParams, $userId);

                    $generatedCount++;
                } catch (\Exception $e) {
                    Log::error('选择性分镜生成失败', [
                        'storyboard_id' => $storyboard->id,
                        'error' => $e->getMessage()
                    ]);

                    $storyboard->markAsFailed();
                    $failedCount++;
                }
            }

            DB::commit();

            Log::info('选择性分镜图片生成完成', [
                'project_id' => $projectId,
                'user_id' => $userId,
                'filters' => $filters,
                'total_matched' => $storyboards->count(),
                'generated_count' => $generatedCount,
                'failed_count' => $failedCount,
                'skipped_count' => $skippedCount
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '选择性分镜图片生成完成',
                'data' => [
                    'project_id' => $projectId,
                    'filters_applied' => $filters,
                    'summary' => [
                        'total_matched' => $storyboards->count(),
                        'generated_count' => $generatedCount,
                        'failed_count' => $failedCount,
                        'skipped_count' => $skippedCount
                    ],
                    'generation_params' => $generationParams
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('选择性分镜图片生成失败', [
                'project_id' => $projectId,
                'user_id' => $userId,
                'filters' => $filters,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '选择性分镜图片生成失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 获取选择性分镜ID列表
     */
    public function getSelectiveStoryboardIds(int $userId, int $projectId, array $filters): array
    {
        try {
            // 验证项目权限
            $projectCheck = $this->checkProjectAccess($userId, $projectId);
            if ($projectCheck['code'] !== ApiCodeEnum::SUCCESS) {
                return [];
            }

            $query = ProjectStoryboard::byProject($projectId);

            // 应用筛选条件
            if (!empty($filters['status'])) {
                $query->whereIn('status', $filters['status']);
            }

            if (isset($filters['has_ai_prompt']) && $filters['has_ai_prompt']) {
                $query->where(function($q) {
                    $q->whereNotNull('ai_prompt')
                      ->where('ai_prompt', '!=', '');
                });
            }

            if (!empty($filters['scene_types'])) {
                $query->whereIn('scene_type', $filters['scene_types']);
            }

            return $query->pluck('id')->toArray();

        } catch (\Exception $e) {
            Log::error('获取选择性分镜ID失败', [
                'user_id' => $userId,
                'project_id' => $projectId,
                'filters' => $filters,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * 获取项目所有分镜ID列表
     */
    public function getAllProjectStoryboardIds(int $userId, int $projectId): array
    {
        try {
            // 验证项目权限
            $projectCheck = $this->checkProjectAccess($userId, $projectId);
            if ($projectCheck['code'] !== ApiCodeEnum::SUCCESS) {
                return [];
            }

            return ProjectStoryboard::byProject($projectId)
                ->pluck('id')
                ->toArray();

        } catch (\Exception $e) {
            Log::error('获取项目分镜ID失败', [
                'user_id' => $userId,
                'project_id' => $projectId,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * 验证分镜ID列表
     */
    public function validateStoryboardIds(int $userId, int $projectId, array $storyboardIds): array
    {
        try {
            // 验证项目权限
            $projectCheck = $this->checkProjectAccess($userId, $projectId);
            if ($projectCheck['code'] !== ApiCodeEnum::SUCCESS) {
                return [];
            }

            return ProjectStoryboard::byProject($projectId)
                ->whereIn('id', $storyboardIds)
                ->pluck('id')
                ->toArray();

        } catch (\Exception $e) {
            Log::error('验证分镜ID失败', [
                'user_id' => $userId,
                'project_id' => $projectId,
                'storyboard_ids' => $storyboardIds,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * 获取未处理的分镜ID列表
     */
    public function getUnprocessedStoryboardIds(int $userId, int $projectId): array
    {
        try {
            // 验证项目权限
            $projectCheck = $this->checkProjectAccess($userId, $projectId);
            if ($projectCheck['code'] !== ApiCodeEnum::SUCCESS) {
                return [];
            }

            return ProjectStoryboard::byProject($projectId)
                ->where(function($query) {
                    $query->whereNull('generated_image_id')
                          ->orWhere('status', ProjectStoryboard::STATUS_FAILED);
                })
                ->where('status', '!=', ProjectStoryboard::STATUS_GENERATING)
                ->pluck('id')
                ->toArray();

        } catch (\Exception $e) {
            Log::error('获取未处理分镜ID失败', [
                'user_id' => $userId,
                'project_id' => $projectId,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * 获取生成任务状态
     */
    public function getGenerationTaskStatus(string $taskId, int $userId): array
    {
        try {
            // 查询包含该任务ID的分镜
            $storyboards = ProjectStoryboard::whereJsonContains('metadata->image_generation_result->task_id', $taskId)
                ->orWhereJsonContains('metadata->image_generation_task_id', $taskId)
                ->get();

            if ($storyboards->isEmpty()) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '任务不存在',
                    'data' => []
                ];
            }

            // 验证用户权限（检查第一个分镜的项目权限）
            $firstStoryboard = $storyboards->first();
            $projectCheck = $this->checkProjectAccess($userId, $firstStoryboard->project_id);
            if ($projectCheck['code'] !== ApiCodeEnum::SUCCESS) {
                return $projectCheck;
            }

            // 统计任务状态
            $totalCount = $storyboards->count();
            $completedCount = $storyboards->where('status', ProjectStoryboard::STATUS_COMPLETED)->count();
            $failedCount = $storyboards->where('status', ProjectStoryboard::STATUS_FAILED)->count();
            $generatingCount = $storyboards->where('status', ProjectStoryboard::STATUS_GENERATING)->count();

            // 计算进度
            $progress = $totalCount > 0 ? (($completedCount + $failedCount) / $totalCount) * 100 : 0;

            // 确定整体状态
            $overallStatus = 'processing';
            if ($generatingCount === 0) {
                if ($failedCount === $totalCount) {
                    $overallStatus = 'failed';
                } elseif ($completedCount + $failedCount === $totalCount) {
                    $overallStatus = 'completed';
                }
            }

            // 构建结果数据
            $resultData = [
                'task_id' => $taskId,
                'status' => $overallStatus,
                'progress' => round($progress, 2),
                'summary' => [
                    'total_storyboards' => $totalCount,
                    'completed' => $completedCount,
                    'failed' => $failedCount,
                    'generating' => $generatingCount,
                    'pending' => $totalCount - $completedCount - $failedCount - $generatingCount
                ],
                'storyboards' => $storyboards->map(function ($storyboard) {
                    return [
                        'id' => $storyboard->id,
                        'status' => $storyboard->status,
                        'generated_image_id' => $storyboard->generated_image_id,
                        'metadata' => $storyboard->metadata['image_generation_result'] ?? null
                    ];
                })->toArray(),
                'updated_at' => now()->format('c')
            ];

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '获取任务状态成功',
                'data' => $resultData
            ];

        } catch (\Exception $e) {
            Log::error('获取生成任务状态失败', [
                'task_id' => $taskId,
                'user_id' => $userId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取任务状态失败',
                'data' => []
            ];
        }
    }
}
