<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Cache;
use App\Models\UserModelPreference;
use Carbon\Carbon;


/**
 * AI服务客户端
 * 
 * 🚨 架构边界规范：环境切换机制实现
 * ✅ 本地开发：调用模拟服务（无真实费用）
 * ✅ 生产环境：调用真实AI平台（产生真实费用）
 * 
 * 环境切换通过 AI_SERVICE_MODE 环境变量控制
 */
class AiServiceClient
{
    /**
     * 调用AI服务
     * 
     * @param string $platform 平台名称 (deepseek, liblib, kling, minimax, volcengine)
     * @param array $data 请求数据
     * @param array $options 额外选项
     * @return array
     */
    public static function call($platform, $data, $options = [])
    {
        $serviceMode = Config::get('ai.service_mode', 'mock');
        
        Log::info("AI服务调用", [
            'platform' => $platform,
            'mode' => $serviceMode,
            'data_size' => strlen(json_encode($data))
        ]);
        
        if ($serviceMode === 'mock') {
            return self::callMockService($platform, $data, $options);
        } else {
            return self::callRealService($platform, $data, $options);
        }
    }
    
    /**
     * 调用模拟服务
     * 
     * @param string $platform
     * @param array $data
     * @param array $options
     * @return array
     */
    private static function callMockService($platform, $data, $options = [])
    {
        $platformConfig = Config::get("ai.platforms.{$platform}");
        if (!$platformConfig) {
            throw new \Exception("不支持的AI平台: {$platform}");
        }
        
        $mockConfig = Config::get('ai.mock_service');
        $url = $mockConfig['base_url'] . $platformConfig['mock_endpoint'];
        $timeout = $options['timeout'] ?? $mockConfig['timeout'];
        
        Log::info("调用模拟服务", [
            'platform' => $platform,
            'url' => $url,
            'timeout' => $timeout
        ]);
        
        try {
            $response = Http::timeout($timeout)
                ->withOptions([
                    'verify' => false, // 模拟服务禁用SSL验证（开发环境）
                    'allow_redirects' => true,
                    'http_errors' => false, // 不抛出HTTP错误异常
                    'curl' => [
                        CURLOPT_SSL_VERIFYPEER => false,
                        CURLOPT_SSL_VERIFYHOST => 0,
                        CURLOPT_FOLLOWLOCATION => true,
                        CURLOPT_MAXREDIRS => 3,
                    ]
                ])
                ->withHeaders([
                    'Content-Type' => 'application/json',
                    'User-Agent' => 'AI-Tool-API/1.0',
                ])
                ->post($url, $data);
            
            if ($response->successful()) {
                $result = $response->json();
                
                Log::info("模拟服务调用成功", [
                    'platform' => $platform,
                    'status' => $response->status(),
                    'response_size' => strlen($response->body())
                ]);
                
                return [
                    'success' => true,
                    'data' => $result,
                    'mode' => 'mock',
                    'platform' => $platform
                ];
            } else {
                Log::error("模拟服务调用失败", [
                    'platform' => $platform,
                    'status' => $response->status(),
                    'error' => $response->body()
                ]);
                
                return [
                    'success' => false,
                    'error' => '模拟服务调用失败',
                    'status' => $response->status(),
                    'mode' => 'mock',
                    'platform' => $platform
                ];
            }
        } catch (\Exception $e) {
            Log::error("模拟服务调用异常", [
                'platform' => $platform,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'error' => '模拟服务调用异常: ' . $e->getMessage(),
                'mode' => 'mock',
                'platform' => $platform
            ];
        }
    }
    
    /**
     * 调用真实服务
     * 
     * @param string $platform
     * @param array $data
     * @param array $options
     * @return array
     */
    private static function callRealService($platform, $data, $options = [])
    {
        $platformConfig = Config::get("ai.platforms.{$platform}");
        if (!$platformConfig || !isset($platformConfig['real_api'])) {
            throw new \Exception("平台 {$platform} 的真实API配置不存在");
        }
        
        $realApi = $platformConfig['real_api'];
        $url = $realApi['base_url'] . $realApi['endpoint'];
        $timeout = $options['timeout'] ?? Config::get('ai.real_service.timeout', 60);
        
        // 检查API密钥
        if (empty($realApi['api_key'])) {
            throw new \Exception("平台 {$platform} 的API密钥未配置");
        }
        
        Log::warning("调用真实AI服务", [
            'platform' => $platform,
            'url' => $url,
            'timeout' => $timeout,
            'warning' => '这将产生真实费用'
        ]);
        
        try {
            $headers = [
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer ' . $realApi['api_key'],
                'User-Agent' => 'AI-Tool-API/1.0',
            ];
            
            // 特殊平台的额外头部
            if ($platform === 'minimax' && !empty($realApi['group_id'])) {
                $headers['GroupId'] = $realApi['group_id'];
            }
            
            $response = Http::timeout($timeout)
                ->withOptions([
                    'verify' => true, // 启用SSL证书验证
                    'allow_redirects' => true,
                    'http_errors' => false, // 不抛出HTTP错误异常
                    'curl' => [
                        CURLOPT_SSL_VERIFYPEER => true,
                        CURLOPT_SSL_VERIFYHOST => 2,
                        CURLOPT_FOLLOWLOCATION => true,
                        CURLOPT_MAXREDIRS => 3,
                    ]
                ])
                ->withHeaders($headers)
                ->post($url, $data);
            
            if ($response->successful()) {
                $result = $response->json();
                
                Log::info("真实AI服务调用成功", [
                    'platform' => $platform,
                    'status' => $response->status(),
                    'response_size' => strlen($response->body())
                ]);
                
                return [
                    'success' => true,
                    'data' => $result,
                    'mode' => 'real',
                    'platform' => $platform
                ];
            } else {
                Log::error("真实AI服务调用失败", [
                    'platform' => $platform,
                    'status' => $response->status(),
                    'error' => $response->body()
                ]);
                
                return [
                    'success' => false,
                    'error' => '真实AI服务调用失败',
                    'status' => $response->status(),
                    'mode' => 'real',
                    'platform' => $platform
                ];
            }
        } catch (\Exception $e) {
            Log::error("真实AI服务调用异常", [
                'platform' => $platform,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'error' => '真实AI服务调用异常: ' . $e->getMessage(),
                'mode' => 'real',
                'platform' => $platform
            ];
        }
    }
    
    /**
     * 获取当前服务模式
     * 
     * @return string mock|real
     */
    public static function getServiceMode()
    {
        return Config::get('ai.service_mode', 'mock');
    }
    
    /**
     * 检查是否为模拟模式
     * 
     * @return bool
     */
    public static function isMockMode()
    {
        return self::getServiceMode() === 'mock';
    }
    
    /**
     * 获取支持的平台列表
     *
     * @return array
     */
    public static function getSupportedPlatforms()
    {
        return array_keys(Config::get('ai.platforms', []));
    }

    /**
     * 获取可用AI平台选项列表（用户选择用）
     *
     * @param string $taskType 任务类型 (image_generation, video_generation, text_generation, voice_synthesis, sound_generation)
     * @param int|null $userId 用户ID（用于个性化推荐）
     * @return array
     */
    public static function getPlatformOptions(string $taskType, ?int $userId = null): array
    {
        try {
            // 获取支持该任务类型的平台
            $supportedPlatforms = self::getPlatformsByTaskType($taskType);

            if (empty($supportedPlatforms)) {
                return [
                    'success' => false,
                    'error' => "不支持的任务类型: {$taskType}",
                    'data' => []
                ];
            }

            $platformOptions = [];

            foreach ($supportedPlatforms as $platformKey) {
                $platformConfig = Config::get("ai.platforms.{$platformKey}");
                if (!$platformConfig) {
                    continue;
                }

                // 获取平台详细信息
                $platformInfo = self::buildPlatformInfo($platformKey, $platformConfig, $taskType);

                // 如果有用户ID，添加个性化信息
                if ($userId) {
                    $platformInfo = self::addUserPersonalization($platformInfo, $userId, $taskType);
                }

                $platformOptions[] = $platformInfo;
            }

            // 根据用户偏好排序
            if ($userId) {
                $platformOptions = self::sortByUserPreference($platformOptions, $userId, $taskType);
            }

            return [
                'success' => true,
                'data' => [
                    'task_type' => $taskType,
                    'platforms' => $platformOptions,
                    'total_count' => count($platformOptions),
                    'user_id' => $userId,
                    'timestamp' => Carbon::now()->toISOString()
                ]
            ];

        } catch (\Exception $e) {
            Log::error("获取平台选项失败", [
                'task_type' => $taskType,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => '获取平台选项失败: ' . $e->getMessage(),
                'data' => []
            ];
        }
    }
    
    /**
     * 验证平台配置
     * 
     * @param string $platform
     * @return array
     */
    public static function validatePlatformConfig($platform)
    {
        $config = Config::get("ai.platforms.{$platform}");
        if (!$config) {
            return ['valid' => false, 'error' => '平台不存在'];
        }
        
        $serviceMode = self::getServiceMode();
        
        if ($serviceMode === 'real') {
            if (!isset($config['real_api']) || empty($config['real_api']['api_key'])) {
                return ['valid' => false, 'error' => '真实API配置不完整'];
            }
        }
        
        return ['valid' => true, 'mode' => $serviceMode];
    }

    /**
     * 根据任务类型获取支持的平台
     * 🚨 重构：从配置文件读取任务类型到平台的映射
     *
     * @param string $taskType
     * @return array
     */
    private static function getPlatformsByTaskType(string $taskType): array
    {
        // 从配置文件获取任务类型到平台的映射
        return config('ai.business_type_mappings.task_to_platforms.' . $taskType, []);
    }

    /**
     * 构建平台信息
     *
     * @param string $platformKey
     * @param array $platformConfig
     * @param string $taskType
     * @return array
     */
    private static function buildPlatformInfo(string $platformKey, array $platformConfig, string $taskType): array
    {
        // 获取成本配置
        $costMultiplier = Config::get("ai.cost_settings.platform_multipliers.{$platformKey}", 1.0);
        $baseCost = Config::get('ai.cost_settings.base_cost_per_token', 0.0001);
        $estimatedCost = $baseCost * $costMultiplier;

        // 获取预估时间（基于平台和任务类型）
        $estimatedTime = self::getEstimatedTime($platformKey, $taskType);

        // 获取平台特色
        $features = self::getPlatformFeatures($platformKey, $taskType);

        return [
            'platform_key' => $platformKey,
            'name' => $platformConfig['name'],
            'description' => $platformConfig['description'],
            'supports' => $platformConfig['supports'] ?? [],
            'pricing' => [
                'cost_per_request' => round($estimatedCost, 6),
                'cost_multiplier' => $costMultiplier,
                'currency' => 'CNY'
            ],
            'performance' => [
                'estimated_time' => $estimatedTime,
                'timeout' => $platformConfig['timeout'] ?? 60
            ],
            'features' => $features,
            'model' => $platformConfig['model'] ?? 'default',
            'availability' => self::checkPlatformAvailability($platformKey)
        ];
    }

    /**
     * 获取预估时间
     * 🚨 重构：从配置文件读取平台时间预估
     *
     * @param string $platform
     * @param string $taskType
     * @return string
     */
    private static function getEstimatedTime(string $platform, string $taskType): string
    {
        // 从配置文件获取时间预估
        return config("ai.platform_time_estimates.{$platform}.{$taskType}", '30-60秒');
    }

    /**
     * 获取平台特色
     *
     * @param string $platform
     * @param string $taskType
     * @return array
     */
    private static function getPlatformFeatures(string $platform, string $taskType): array
    {
        $platformFeatures = [
            'deepseek' => [
                'text_generation' => ['高质量文本', '逻辑推理强', '中文优化'],
                'text_generation_story' => ['剧情连贯', '角色丰富', '情节生动'],
                'character_generation' => ['角色立体', '性格鲜明', '背景详细']
            ],
            'liblib' => [
                'image_generation' => ['画质精美', '风格多样', '细节丰富', '专业级质量']
            ],
            'kling' => [
                'image_generation' => ['生成速度快', '风格独特', '创意丰富'],
                'video_generation' => ['视频流畅', '画面清晰', '动作自然', '行业领先']
            ],
            'minimax' => [
                'image_generation' => ['多模态支持', '风格可控', '批量生成'],
                'video_generation' => ['多种分辨率', '自定义时长', '特效丰富'],
                'text_generation' => ['多语言支持', '上下文理解', '格式灵活'],
                'voice_synthesis' => ['音质清晰', '情感丰富', '多种音色'],
                'sound_generation' => ['音效逼真', '环境音丰富', '可定制'],
                'music_generation' => ['风格多样', '节奏准确', '旋律优美']
            ],
            'volcengine' => [
                'voice_synthesis' => ['音质专业', '发音准确', '情感自然', '火山引擎技术'],
                'sound_generation' => ['音效专业', '场景丰富', '质量稳定'],
                'text_generation' => ['豆包模型', '理解准确', '回复自然']
            ]
        ];

        return $platformFeatures[$platform][$taskType] ?? ['AI生成', '质量保证'];
    }

    /**
     * 检查平台可用性
     *
     * @param string $platform
     * @return array
     */
    private static function checkPlatformAvailability(string $platform): array
    {
        $serviceMode = self::getServiceMode();

        if ($serviceMode === 'mock') {
            return [
                'status' => 'available',
                'mode' => 'mock',
                'message' => '模拟模式，始终可用'
            ];
        }

        // 在真实模式下检查平台配置
        $config = Config::get("ai.platforms.{$platform}");
        if (!$config || !isset($config['real_api']) || empty($config['real_api']['api_key'])) {
            return [
                'status' => 'unavailable',
                'mode' => 'real',
                'message' => 'API配置不完整'
            ];
        }

        return [
            'status' => 'available',
            'mode' => 'real',
            'message' => '配置完整，可用'
        ];
    }

    /**
     * 添加用户个性化信息
     *
     * @param array $platformInfo
     * @param int $userId
     * @param string $taskType
     * @return array
     */
    private static function addUserPersonalization(array $platformInfo, int $userId, string $taskType): array
    {
        try {
            // 获取用户偏好
            $userPreference = UserModelPreference::where('user_id', $userId)
                ->where('business_type', $taskType)
                ->first();

            $personalization = [
                'user_priority' => 50, // 默认优先级
                'usage_count' => 0,
                'last_used' => null,
                'is_preferred' => false,
                'recommendation_score' => 0.5
            ];

            if ($userPreference) {
                $platformKey = $platformInfo['platform_key'];
                $personalization['user_priority'] = $userPreference->getPlatformPriority($platformKey);
                $personalization['is_preferred'] = ($userPreference->preferred_platform === $platformKey);
                $personalization['usage_count'] = $userPreference->usage_count ?? 0;
                $personalization['last_used'] = $userPreference->last_used_at?->toISOString();

                // 计算推荐分数
                $personalization['recommendation_score'] = self::calculateRecommendationScore(
                    $platformInfo, $userPreference, $taskType
                );
            }

            $platformInfo['personalization'] = $personalization;

            return $platformInfo;

        } catch (\Exception $e) {
            Log::warning("添加用户个性化信息失败", [
                'user_id' => $userId,
                'platform' => $platformInfo['platform_key'] ?? 'unknown',
                'error' => $e->getMessage()
            ]);

            // 失败时返回默认个性化信息
            $platformInfo['personalization'] = [
                'user_priority' => 50,
                'usage_count' => 0,
                'last_used' => null,
                'is_preferred' => false,
                'recommendation_score' => 0.5
            ];

            return $platformInfo;
        }
    }

    /**
     * 计算推荐分数
     *
     * @param array $platformInfo
     * @param UserModelPreference $userPreference
     * @param string $taskType
     * @return float
     */
    private static function calculateRecommendationScore(array $platformInfo, UserModelPreference $userPreference, string $taskType): float
    {
        $score = 0.5; // 基础分数

        // 用户优先级权重 (40%)
        $priorityScore = $userPreference->getPlatformPriority($platformInfo['platform_key']) / 100;
        $score += $priorityScore * 0.4;

        // 使用频率权重 (30%)
        $usageScore = min($userPreference->usage_count / 10, 1.0); // 最多10次使用达到满分
        $score += $usageScore * 0.3;

        // 成本偏好权重 (20%)
        if ($userPreference->cost_optimization) {
            $costScore = 1.0 - ($platformInfo['pricing']['cost_multiplier'] - 1.0) / 2.0;
            $score += max(0, $costScore) * 0.2;
        } else {
            $score += 0.1; // 不关心成本时给予中等分数
        }

        // 最近使用权重 (10%)
        if ($userPreference->last_used_at) {
            $daysSinceLastUse = $userPreference->last_used_at->diffInDays(Carbon::now());
            $recentScore = max(0, 1.0 - $daysSinceLastUse / 30); // 30天内使用过的给予加分
            $score += $recentScore * 0.1;
        }

        return min(1.0, max(0.0, $score));
    }

    /**
     * 根据用户偏好排序平台
     *
     * @param array $platformOptions
     * @param int $userId
     * @param string $taskType
     * @return array
     */
    private static function sortByUserPreference(array $platformOptions, int $userId, string $taskType): array
    {
        usort($platformOptions, function ($a, $b) {
            // 首先按推荐分数排序
            $scoreA = $a['personalization']['recommendation_score'] ?? 0.5;
            $scoreB = $b['personalization']['recommendation_score'] ?? 0.5;

            if (abs($scoreA - $scoreB) > 0.1) {
                return $scoreB <=> $scoreA; // 分数高的排前面
            }

            // 分数相近时，按用户优先级排序
            $priorityA = $a['personalization']['user_priority'] ?? 50;
            $priorityB = $b['personalization']['user_priority'] ?? 50;

            if ($priorityA !== $priorityB) {
                return $priorityB <=> $priorityA; // 优先级高的排前面
            }

            // 最后按使用次数排序
            $usageA = $a['personalization']['usage_count'] ?? 0;
            $usageB = $b['personalization']['usage_count'] ?? 0;

            return $usageB <=> $usageA; // 使用次数多的排前面
        });

        return $platformOptions;
    }

    /**
     * 验证用户选择的平台
     *
     * @param string $platform
     * @param string $taskType
     * @return array
     */
    public static function validatePlatformChoice(string $platform, string $taskType): array
    {
        try {
            // 检查平台是否存在
            $platformConfig = Config::get("ai.platforms.{$platform}");
            if (!$platformConfig) {
                return [
                    'valid' => false,
                    'error' => "平台不存在: {$platform}",
                    'code' => 'PLATFORM_NOT_FOUND'
                ];
            }

            // 检查平台是否支持该任务类型
            $supportedPlatforms = self::getPlatformsByTaskType($taskType);
            if (!in_array($platform, $supportedPlatforms)) {
                return [
                    'valid' => false,
                    'error' => "平台 {$platform} 不支持任务类型 {$taskType}",
                    'code' => 'TASK_TYPE_NOT_SUPPORTED',
                    'supported_platforms' => $supportedPlatforms
                ];
            }

            // 检查平台可用性
            $availability = self::checkPlatformAvailability($platform);
            if ($availability['status'] !== 'available') {
                return [
                    'valid' => false,
                    'error' => "平台当前不可用: {$availability['message']}",
                    'code' => 'PLATFORM_UNAVAILABLE',
                    'availability' => $availability
                ];
            }

            return [
                'valid' => true,
                'platform' => $platform,
                'task_type' => $taskType,
                'config' => $platformConfig,
                'availability' => $availability
            ];

        } catch (\Exception $e) {
            Log::error("验证平台选择失败", [
                'platform' => $platform,
                'task_type' => $taskType,
                'error' => $e->getMessage()
            ]);

            return [
                'valid' => false,
                'error' => '验证平台选择时发生错误: ' . $e->getMessage(),
                'code' => 'VALIDATION_ERROR'
            ];
        }
    }

    /**
     * 带用户选择的AI调用方法
     *
     * @param string $platform 用户选择的平台
     * @param string $taskType 任务类型
     * @param array $data 请求数据
     * @param int $userId 用户ID
     * @param array $options 额外选项
     * @return array
     */
    public static function callWithUserChoice(string $platform, string $taskType, array $data, int $userId, array $options = []): array
    {
        try {
            // 验证用户选择
            $validation = self::validatePlatformChoice($platform, $taskType);
            if (!$validation['valid']) {
                return [
                    'success' => false,
                    'error' => $validation['error'],
                    'code' => $validation['code'] ?? 'VALIDATION_FAILED',
                    'data' => null
                ];
            }

            // 记录用户选择开始
            $startTime = microtime(true);

            // 调用AI服务
            $result = self::call($platform, $data, $options);

            // 计算执行时间
            $executionTime = microtime(true) - $startTime;

            // 记录用户偏好（异步）
            self::recordUserPreference($userId, $platform, $taskType, $result['success'], $executionTime);

            // 增强返回结果
            if ($result['success']) {
                $result['user_choice'] = [
                    'platform' => $platform,
                    'task_type' => $taskType,
                    'user_id' => $userId,
                    'execution_time' => round($executionTime, 3),
                    'timestamp' => Carbon::now()->toISOString()
                ];
            }

            return $result;

        } catch (\Exception $e) {
            Log::error("用户选择调用失败", [
                'platform' => $platform,
                'task_type' => $taskType,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => '调用AI服务失败: ' . $e->getMessage(),
                'code' => 'CALL_FAILED',
                'data' => null
            ];
        }
    }

    /**
     * 记录用户偏好
     *
     * @param int $userId
     * @param string $platform
     * @param string $taskType
     * @param bool $success
     * @param float $executionTime
     * @return void
     */
    public static function recordUserPreference(int $userId, string $platform, string $taskType, bool $success, float $executionTime): void
    {
        try {
            // 查找或创建用户偏好记录
            $preference = UserModelPreference::firstOrCreate(
                [
                    'user_id' => $userId,
                    'business_type' => $taskType
                ],
                [
                    'preferred_platform' => $platform,
                    'platform_priorities' => json_encode([$platform => 80]),
                    'selection_criteria' => json_encode([]),
                    'usage_count' => 0,
                    'success_rate' => 0.0,
                    'avg_response_time' => 0.0,
                    'cost_optimization' => false,
                    'last_used_at' => Carbon::now()
                ]
            );

            // 更新使用统计
            $preference->usage_count = ($preference->usage_count ?? 0) + 1;
            $preference->last_used_at = Carbon::now();

            // 更新成功率
            $totalCalls = $preference->usage_count;
            $previousSuccessCount = round(($preference->success_rate ?? 0) * ($totalCalls - 1));
            $newSuccessCount = $previousSuccessCount + ($success ? 1 : 0);
            $preference->success_rate = $newSuccessCount / $totalCalls;

            // 更新平均响应时间
            $previousAvgTime = $preference->avg_response_time ?? 0;
            $preference->avg_response_time = (($previousAvgTime * ($totalCalls - 1)) + $executionTime) / $totalCalls;

            // 更新平台优先级
            $priorities = json_decode($preference->platform_priorities ?? '{}', true);
            if (!isset($priorities[$platform])) {
                $priorities[$platform] = 50; // 默认优先级
            }

            // 根据成功率调整优先级
            if ($success) {
                $priorities[$platform] = min(100, $priorities[$platform] + 2);
            } else {
                $priorities[$platform] = max(10, $priorities[$platform] - 5);
            }

            $preference->platform_priorities = json_encode($priorities);

            // 如果这是用户最常用的平台，设为首选
            $maxUsagePlatform = array_keys($priorities, max($priorities))[0];
            if ($maxUsagePlatform === $platform) {
                $preference->preferred_platform = $platform;
            }

            $preference->save();

            // 更新缓存
            $cacheKey = "user_platform_preference:{$userId}:{$taskType}";
            Cache::put($cacheKey, $preference->toArray(), 3600); // 缓存1小时

            Log::info("用户偏好记录更新成功", [
                'user_id' => $userId,
                'platform' => $platform,
                'task_type' => $taskType,
                'usage_count' => $preference->usage_count,
                'success_rate' => $preference->success_rate
            ]);

        } catch (\Exception $e) {
            Log::error("记录用户偏好失败", [
                'user_id' => $userId,
                'platform' => $platform,
                'task_type' => $taskType,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 获取用户推荐平台
     *
     * @param int $userId
     * @param string $taskType
     * @param int $limit
     * @return array
     */
    public static function getUserRecommendations(int $userId, string $taskType, int $limit = 3): array
    {
        try {
            // 获取平台选项
            $platformOptions = self::getPlatformOptions($taskType, $userId);

            if (!$platformOptions['success']) {
                return $platformOptions;
            }

            $platforms = $platformOptions['data']['platforms'];

            // 限制返回数量
            $recommendations = array_slice($platforms, 0, $limit);

            // 添加推荐理由
            foreach ($recommendations as &$platform) {
                $platform['recommendation_reason'] = self::generateRecommendationReason($platform, $userId);
            }

            return [
                'success' => true,
                'data' => [
                    'user_id' => $userId,
                    'task_type' => $taskType,
                    'recommendations' => $recommendations,
                    'total_available' => count($platforms),
                    'timestamp' => Carbon::now()->toISOString()
                ]
            ];

        } catch (\Exception $e) {
            Log::error("获取用户推荐失败", [
                'user_id' => $userId,
                'task_type' => $taskType,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => '获取推荐失败: ' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 生成推荐理由
     *
     * @param array $platform
     * @param int $userId
     * @return string
     */
    private static function generateRecommendationReason(array $platform, int $userId): string
    {
        $personalization = $platform['personalization'] ?? [];
        $reasons = [];

        if ($personalization['is_preferred'] ?? false) {
            $reasons[] = '您的首选平台';
        }

        if (($personalization['usage_count'] ?? 0) > 0) {
            $reasons[] = "您已使用 {$personalization['usage_count']} 次";
        }

        if (($personalization['recommendation_score'] ?? 0) > 0.8) {
            $reasons[] = '高度匹配您的使用习惯';
        }

        // 基于平台特色的推荐
        $features = $platform['features'] ?? [];
        if (!empty($features)) {
            $topFeature = $features[0] ?? '';
            if ($topFeature) {
                $reasons[] = "特色: {$topFeature}";
            }
        }

        // 基于性能的推荐
        $estimatedTime = $platform['performance']['estimated_time'] ?? '';
        if (strpos($estimatedTime, '秒') !== false) {
            $reasons[] = '响应速度快';
        }

        return empty($reasons) ? '推荐使用' : implode('，', $reasons);
    }
}
